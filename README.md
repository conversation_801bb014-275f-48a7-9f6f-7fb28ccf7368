# Internet Payment Processor API

## نظام معالجة دفعات شركات الإنترنت السورية

هذا المشروع عبارة عن API مبني بـ FastAPI لمعالجة دفعات شركات الإنترنت السورية المختلفة.

## الشركات المدعومة

- **MTS Syria** (Product ID: 32)
- **Sawa Syria** (Product ID: 29)
- **Syriatel Syria** (Product ID: 25)
- **INET Syria** (Product ID: 47)
- **Linet Syria** (Product ID: 46)
- **Takamol Syria** (Product ID: 45)
- **LEMA Syria** (Product ID: 148)
- **Bitakat Syria** (Product ID: 50)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 2. تحديث ملف التكوين

قم بتحديث ملف `config.json` بمعلومات تسجيل الدخول الصحيحة لكل شركة:

```json
{
  "companies": {
    "mts": {
      "credentials": {
        "username": "your_actual_username",
        "password": "your_actual_password"
      }
    }
  }
}
```

### 3. تشغيل الخادم

```bash
python main.py
```

أو باستخدام uvicorn مباشرة:

```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## استخدام API

### نقاط النهاية المتاحة

- `GET /` - معلومات عامة عن API
- `GET /health` - فحص حالة الخادم
- `GET /companies` - قائمة الشركات المدعومة
- `POST /process-payment` - معالجة طلب دفع
- `POST /process-payment-async` - معالجة طلب دفع بشكل غير متزامن

### مثال على طلب الدفع

```bash
curl -X POST "http://localhost:8000/process-payment" \
     -H "Content-Type: application/json" \
     -d '{
       "id": 585241,
       "amount": "24000",
       "number": "7271141",
       "code": null,
       "product": 32,
       "type": "2 MB",
       "date": "2025-08-06T13:00:25.000000Z"
     }'
```

### استجابة API

```json
{
  "success": true,
  "message": "تم الدفع بنجاح",
  "order_id": 585241,
  "status": 1,
  "details": {
    "order_id": 585241,
    "phone": "*********",
    "amount": "24000",
    "company": "MTS"
  }
}
```

### رموز الحالة

- `1` - تم الدفع بنجاح
- `2` - الرقم غير موجود
- `3` - فشل في الدفع
- `4` - المشترك عليه ديون

## بنية المشروع

```
TawasowlApi/
├── main.py                 # الملف الرئيسي للـ API
├── config.json            # ملف التكوين
├── requirements.txt       # متطلبات Python
├── processors/           # معالجات الدفع
│   ├── __init__.py
│   ├── base_processor.py  # الفئة الأساسية للمعالجات
│   ├── mts_processor.py   # معالج MTS
│   ├── sawa_processor.py  # معالج Sawa
│   └── ...               # باقي المعالجات
└── logs/                 # ملفات السجلات
```

## التطوير والتخصيص

### إضافة شركة جديدة

1. أنشئ معالج جديد في مجلد `processors/`
2. اجعله يرث من `BasePaymentProcessor`
3. نفذ الدوال المطلوبة: `login`, `search_subscriber`, `process_payment`
4. أضف معلومات الشركة في `config.json`
5. أضف المعالج في `main.py`

### تخصيص المعالجات

كل معالج يمكن تخصيصه حسب متطلبات الشركة:

- تنسيق أرقام الهاتف
- حدود المبالغ
- فترات التحقق من التكرار
- طرق تسجيل الدخول المختلفة

## الأمان

- تأكد من تحديث كلمات المرور في `config.json`
- استخدم HTTPS في الإنتاج
- قم بتشفير ملف التكوين إذا لزم الأمر
- راقب ملفات السجلات للأنشطة المشبوهة

## الدعم والصيانة

- تحقق من ملفات السجلات في مجلد `logs/`
- راقب استجابة API باستخدام `/health`
- قم بتحديث المعالجات عند تغيير مواقع الشركات

## ملاحظات مهمة

- هذا المشروع للاستخدام التعليمي والتطويري
- تأكد من الحصول على الإذن المناسب قبل الاستخدام التجاري
- قم بإجراء اختبارات شاملة قبل النشر في الإنتاج
