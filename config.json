{"companies": {"mts": {"product_id": 32, "name": "MTS Syria", "login_url": "https://mts.sy/login", "search_url": "https://mts.sy/search", "payment_url": "https://mts.sy/payment", "credentials": {"username": "your_mts_username", "password": "your_mts_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "41", "required": true}}, "sawa": {"product_id": 29, "name": "Sawa Syria", "login_url": "https://sawa.sy/login", "search_url": "https://sawa.sy/search", "payment_url": "https://sawa.sy/payment", "credentials": {"username": "your_sawa_username", "password": "your_sawa_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 1440}, "phone_format": {"prefix": "", "required": false}}, "syriatel": {"product_id": 25, "name": "Syriatel Syria", "login_url": "https://syriatel.sy/login", "search_url": "https://syriatel.sy/search", "payment_url": "https://syriatel.sy/payment", "credentials": {"username": "your_syriatel_username", "password": "your_syriatel_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}}, "inet": {"product_id": 47, "name": "INET Syria", "login_url": "https://inet.sy/login", "search_url": "https://inet.sy/search", "payment_url": "https://inet.sy/payment", "credentials": {"username": "your_inet_username", "password": "your_inet_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}}, "linet": {"product_id": 46, "name": "Linet Syria", "login_url": "https://linet.sy/login", "search_url": "https://linet.sy/search", "payment_url": "https://linet.sy/payment", "credentials": {"username": "your_linet_username", "password": "your_linet_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}}, "takamol": {"product_id": 45, "name": "Takamol Syria", "login_url": "https://takamol.sy/login", "search_url": "https://takamol.sy/search", "payment_url": "https://takamol.sy/payment", "credentials": {"username": "your_takamol_username", "password": "your_takamol_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}}, "lema": {"product_id": 148, "name": "LEMA Syria", "login_url": "https://lema.sy/login", "search_url": "https://lema.sy/search", "payment_url": "https://lema.sy/payment", "credentials": {"username": "your_lema_username", "password": "your_lema_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}}, "bitakat": {"product_id": 50, "name": "Bitakat Syria", "login_url": "https://bitakat.sy/login", "search_url": "https://bitakat.sy/search", "payment_url": "https://bitakat.sy/payment", "credentials": {"username": "your_bitakat_username", "password": "your_bitakat_password"}, "limits": {"max_amount": 100000, "duplicate_check_minutes": 5}, "phone_format": {"prefix": "", "required": false}}}, "api_settings": {"timeout": 30, "retry_attempts": 3, "log_level": "INFO"}}