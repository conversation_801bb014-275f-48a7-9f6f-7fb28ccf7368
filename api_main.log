2025-08-07 15:47:13,271 - __main__ - INFO - Starting Internet Payment Processor API...
2025-08-07 15:47:16,628 - watchfiles.main - INFO - 4 changes detected
2025-08-07 15:52:11,018 - httpx - INFO - HTTP Request: GET http://testserver/ "HTTP/1.1 200 OK"
2025-08-07 15:52:11,032 - httpx - INFO - HTTP Request: GET http://testserver/health "HTTP/1.1 200 OK"
2025-08-07 15:52:11,054 - httpx - INFO - HTTP Request: GET http://testserver/companies "HTTP/1.1 200 OK"
2025-08-07 15:52:11,064 - main - INFO - Received payment request: Order ID 585241, Product 32, Amount 24000, Number 7271141
2025-08-07 15:52:11,065 - main - INFO - Processing payment for MTS - Order ID: ***********-08-07 15:52:11,065 - MTSProcessor - INFO - Processing MTS payment - Order: 585241, Phone: 7271141, Amount: 24000
2025-08-07 15:52:11,066 - MTSProcessor - INFO - Attempting to login to MTS system...
2025-08-07 15:52:12,556 - MTSProcessor - INFO - Login page response status: 400
2025-08-07 15:52:13,775 - MTSProcessor - INFO - Login response status: 400
2025-08-07 15:52:13,818 - MTSProcessor - INFO - Login response URL: https://mts.sy/login
2025-08-07 15:52:13,818 - MTSProcessor - ERROR - MTS login failed with status: 400
2025-08-07 15:52:13,920 - main - INFO - Payment processing completed for Order ID 585241: Success=False, Status=3
2025-08-07 15:52:13,962 - httpx - INFO - HTTP Request: POST http://testserver/process-payment "HTTP/1.1 200 OK"
