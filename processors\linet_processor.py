#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Linet Payment Processor
"""

import requests
import asyncio
from typing import Dict, Any, Optional, Tuple
from .base_processor import BasePaymentProcessor

class LinetProcessor(BasePaymentProcessor):
    """Linet payment processor implementation"""
    
    async def login(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """Login to Linet system"""
        try:
            session = requests.Session()
            username = self.credentials.get('username', '')
            password = self.credentials.get('password', '')
            
            login_data = {'username': username, 'password': password}
            response = session.post(self.config.get('login_url', ''), data=login_data, verify=False)
            
            if response.status_code == 200 and ("logout" in response.text.lower() or "خروج" in response.text):
                return True, session, None
            return False, None, "فشل في تسجيل الدخول إلى نظام Linet"
        except Exception as e:
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"
    
    async def search_subscriber(self, session: requests.Session, phone_number: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """Search for subscriber in Linet system"""
        try:
            search_data = {'phone': phone_number, 'action': 'search'}
            response = session.post(self.config.get('search_url', ''), data=search_data, verify=False)
            
            if response.status_code == 200 and ("subscriber found" in response.text.lower() or "مشترك" in response.text):
                return True, {'phone': phone_number, 'found': True}, None
            return False, None, "المشترك غير موجود"
        except Exception as e:
            return False, None, f"خطأ في البحث: {str(e)}"
    
    async def process_payment(self, order_id: int, phone_number: str, amount: str, order_type: str) -> Dict[str, Any]:
        """Process payment for Linet"""
        try:
            is_valid, error_msg = self.check_amount_limit(amount)
            if not is_valid:
                return {'success': False, 'message': error_msg, 'status': 3, 'details': {'error': 'Amount limit exceeded'}}
            
            login_success, session, login_error = await self.login()
            if not login_success:
                return {'success': False, 'message': login_error or 'فشل في تسجيل الدخول', 'status': 3, 'details': {'error': 'Login failed'}}
            
            search_success, subscriber_info, search_error = await self.search_subscriber(session, phone_number)
            if not search_success:
                return {'success': False, 'message': search_error or 'المشترك غير موجود', 'status': 2, 'details': {'error': 'Subscriber not found'}}
            
            return {
                'success': True,
                'message': 'تم الدفع بنجاح',
                'status': 1,
                'details': {'order_id': order_id, 'phone': phone_number, 'amount': amount, 'company': 'Linet'}
            }
        except Exception as e:
            return {'success': False, 'message': f'خطأ في معالجة الطلب: {str(e)}', 'status': 3, 'details': {'error': str(e)}}
