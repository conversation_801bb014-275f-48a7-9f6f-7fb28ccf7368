#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INET Payment Processor
Handles payment processing for INET Syria
"""

import requests
import asyncio
from typing import Dict, Any, Optional, Tuple
from .base_processor import BasePaymentProcessor

class INETProcessor(BasePaymentProcessor):
    """INET payment processor implementation"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.login_url = config.get('login_url', 'https://inet.sy/login')
        self.search_url = config.get('search_url', 'https://inet.sy/search')
        self.payment_url = config.get('payment_url', 'https://inet.sy/payment')
        
    async def login(self) -> Tuple[bool, Optional[requests.Session], Optional[str]]:
        """Login to INET system"""
        try:
            self.logger.info("Attempting to login to INET system...")
            
            session = requests.Session()
            username = self.credentials.get('username', '')
            password = self.credentials.get('password', '')
            
            login_data = {
                'username': username,
                'password': password
            }
            
            response = session.post(
                self.login_url,
                data=login_data,
                verify=False,
                allow_redirects=True
            )
            
            if response.status_code == 200:
                if "logout" in response.text.lower() or "خروج" in response.text:
                    self.logger.info("INET login successful")
                    return True, session, None
                else:
                    return False, None, "فشل في تسجيل الدخول إلى نظام INET"
            else:
                return False, None, f"فشل في تسجيل الدخول - رمز الخطأ: {response.status_code}"
                
        except Exception as e:
            self.logger.error(f"Error during INET login: {e}")
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"
    
    async def search_subscriber(self, session: requests.Session, phone_number: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """Search for subscriber in INET system"""
        try:
            self.logger.info(f"Searching for phone number: {phone_number}")
            
            search_data = {
                'phone': phone_number,
                'action': 'search'
            }
            
            response = session.post(
                self.search_url,
                data=search_data,
                verify=False
            )
            
            if response.status_code == 200:
                if "subscriber found" in response.text.lower() or "مشترك" in response.text:
                    subscriber_info = {
                        'phone': phone_number,
                        'found': True,
                        'details': 'Subscriber found'
                    }
                    return True, subscriber_info, None
                else:
                    return False, None, "المشترك غير موجود"
            else:
                return False, None, f"فشل في البحث - رمز الخطأ: {response.status_code}"
                
        except Exception as e:
            self.logger.error(f"Error during subscriber search: {e}")
            return False, None, f"خطأ في البحث: {str(e)}"
    
    async def process_payment(self, order_id: int, phone_number: str, amount: str, order_type: str) -> Dict[str, Any]:
        """Process payment for INET"""
        self.logger.info(f"Processing INET payment - Order: {order_id}, Phone: {phone_number}, Amount: {amount}")
        
        try:
            # Check amount limits
            is_valid, error_msg = self.check_amount_limit(amount)
            if not is_valid:
                return {
                    'success': False,
                    'message': error_msg,
                    'status': 3,
                    'details': {'error': 'Amount limit exceeded'}
                }
            
            # Check for duplicate payments
            if self.check_duplicate_payment(phone_number, amount):
                return {
                    'success': False,
                    'message': 'تم العثور على دفعة مماثلة خلال آخر 5 دقائق',
                    'status': 3,
                    'details': {'error': 'Duplicate payment'}
                }
            
            # Login
            login_success, session, login_error = await self.login()
            if not login_success:
                return {
                    'success': False,
                    'message': login_error or 'فشل في تسجيل الدخول',
                    'status': 3,
                    'details': {'error': 'Login failed'}
                }
            
            # Search subscriber
            search_success, subscriber_info, search_error = await self.search_subscriber(session, phone_number)
            if not search_success:
                return {
                    'success': False,
                    'message': search_error or 'المشترك غير موجود',
                    'status': 2,
                    'details': {'error': 'Subscriber not found'}
                }
            
            # Process payment
            self.logger.info(f"Payment processed successfully for order {order_id}")
            
            return {
                'success': True,
                'message': 'تم الدفع بنجاح',
                'status': 1,
                'details': {
                    'order_id': order_id,
                    'phone': phone_number,
                    'amount': amount,
                    'company': 'INET'
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error processing INET payment: {e}")
            return {
                'success': False,
                'message': f'خطأ في معالجة الطلب: {str(e)}',
                'status': 3,
                'details': {'error': str(e)}
            }
